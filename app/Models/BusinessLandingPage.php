<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class BusinessLandingPage extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'custom_slug',
        'custom_domain',
        'domain_type',
        'ssl_enabled',
        'page_title',
        'page_description',
        'theme',
        'theme_config',
        'theme_customization',
        'custom_css',
        'last_customized_at',
        'logo_url',
        'branding_config',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'open_graph_config',
        'schema_markup',
        'hero_section',
        'about_section',
        'services_section',
        'contact_section',
        'testimonials_section',
        'gallery_section',
        'custom_sections',
        'booking_enabled',
        'booking_config',
        'booking_button_text',
        'booking_button_color',
        'google_analytics_id',
        'facebook_pixel_id',
        'tracking_codes',
        'cache_enabled',
        'cache_duration',
        'last_generated_at',
        'is_published',
        'is_indexed',
        'published_at',
    ];

    protected $casts = [
        'ssl_enabled' => 'boolean',
        'theme_config' => 'array',
        'theme_customization' => 'array',
        'last_customized_at' => 'datetime',
        'branding_config' => 'array',
        'open_graph_config' => 'array',
        'schema_markup' => 'array',
        'hero_section' => 'array',
        'about_section' => 'array',
        'services_section' => 'array',
        'contact_section' => 'array',
        'testimonials_section' => 'array',
        'gallery_section' => 'array',
        'custom_sections' => 'array',
        'booking_enabled' => 'boolean',
        'booking_config' => 'array',
        'tracking_codes' => 'array',
        'cache_enabled' => 'boolean',
        'is_published' => 'boolean',
        'is_indexed' => 'boolean',
        'last_generated_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($landingPage) {
            if (empty($landingPage->custom_slug)) {
                $landingPage->custom_slug = Str::slug($landingPage->business->name);
            }

            // Ensure unique slug
            $originalSlug = $landingPage->custom_slug;
            $counter = 1;
            while (static::where('custom_slug', $landingPage->custom_slug)->exists()) {
                $landingPage->custom_slug = $originalSlug . '-' . $counter;
                $counter++;
            }
        });

        static::saving(function ($landingPage) {
            if ($landingPage->is_published && !$landingPage->published_at) {
                $landingPage->published_at = now();
            }
        });
    }

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function sections(): HasMany
    {
        return $this->hasMany(BusinessLandingPageSection::class)->orderBy('sort_order');
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeByDomainType($query, $type)
    {
        return $query->where('domain_type', $type);
    }

    // Helper methods
    public function getFullUrlAttribute()
    {
        switch ($this->domain_type) {
            case 'custom':
                return ($this->ssl_enabled ? 'https://' : 'http://') . $this->custom_domain;
            case 'subdomain':
                return ($this->ssl_enabled ? 'https://' : 'http://') . $this->custom_slug . '.bookkei.com';
            default: // subdirectory
                return url('/' . $this->custom_slug);
        }
    }

    public function getRouteKeyName()
    {
        return 'custom_slug';
    }

    public function generateDefaultSections()
    {
        $defaultSections = [
            [
                'section_type' => 'hero',
                'section_name' => 'Hero Section',
                'content_data' => [
                    'title' => $this->business->name,
                    'subtitle' => $this->business->description ?? 'Welcome to our business',
                    'background_image' => null,
                    'cta_text' => 'Book Now',
                    'cta_url' => '#booking'
                ],
                'sort_order' => 1,
                'is_visible' => true
            ],
            [
                'section_type' => 'about',
                'section_name' => 'About Us',
                'content_data' => [
                    'title' => 'About ' . $this->business->name,
                    'content' => $this->business->description ?? 'Learn more about our business',
                    'image' => null
                ],
                'sort_order' => 2,
                'is_visible' => true
            ],
            [
                'section_type' => 'services',
                'section_name' => 'Our Services',
                'content_data' => [
                    'title' => 'Our Services',
                    'subtitle' => 'Discover what we offer',
                    'show_prices' => true,
                    'layout' => 'grid'
                ],
                'sort_order' => 3,
                'is_visible' => true
            ],
            [
                'section_type' => 'contact',
                'section_name' => 'Contact Us',
                'content_data' => [
                    'title' => 'Get in Touch',
                    'show_map' => true,
                    'show_hours' => true,
                    'show_contact_form' => true
                ],
                'sort_order' => 4,
                'is_visible' => true
            ]
        ];

        foreach ($defaultSections as $sectionData) {
            $this->sections()->create($sectionData);
        }
    }

    public function generateSitemap()
    {
        // Generate XML sitemap for the landing page
        $urls = [
            [
                'url' => $this->full_url,
                'lastmod' => $this->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '1.0'
            ]
        ];

        // Add service pages if they exist
        if ($this->business->services()->where('is_active', true)->exists()) {
            $urls[] = [
                'url' => $this->full_url . '/services',
                'lastmod' => $this->business->services()->max('updated_at'),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ];
        }

        return $urls;
    }
}
