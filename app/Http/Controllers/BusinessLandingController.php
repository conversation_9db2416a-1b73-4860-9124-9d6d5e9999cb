<?php

namespace App\Http\Controllers;

use App\Models\Business;
use App\Models\BusinessLandingPage;
use App\Models\Service;
use App\Services\LandingPageCacheService;
use App\Services\LandingPageAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\View;

class BusinessLandingController extends Controller
{
    protected $cacheService;
    protected $analyticsService;

    public function __construct(LandingPageCacheService $cacheService, LandingPageAnalyticsService $analyticsService)
    {
        $this->cacheService = $cacheService;
        $this->analyticsService = $analyticsService;
    }

    /**
     * Display the main landing page for a business.
     */
    public function index($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $pageData = $this->getPageData($business);

        // Track page view
        $this->trackPageView($landingPage);

        return view('landing-page.index', compact('landingPage', 'business', 'pageData'));
    }

    /**
     * Display the services page for a business.
     */
    public function services($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $services = $this->getBusinessServices($business);
        $serviceSettings = $business->landingServiceSettings;

        return view('landing-page.services', compact('landingPage', 'business', 'services', 'serviceSettings'));
    }

    /**
     * Display individual service detail page.
     */
    public function serviceDetail($businessSlug, $serviceSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $service = $this->getServiceBySlug($business, $serviceSlug);

        if (!$service || !$service->is_public) {
            abort(404, 'Service not found or not available.');
        }

        $serviceSettings = $business->landingServiceSettings;
        $relatedServices = $this->getRelatedServices($service);

        // Track service view
        $this->trackServiceView($service);

        return view('landing-page.service-detail', compact('landingPage', 'business', 'service', 'serviceSettings', 'relatedServices'));
    }

    /**
     * Display the booking page for a business.
     */
    public function booking($businessSlug, Request $request)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published || !$landingPage->booking_enabled) {
            abort(404, 'Booking not available for this business.');
        }

        $business = $landingPage->business;
        $services = $this->getBusinessServices($business);
        $selectedService = null;

        // Pre-select service if provided
        if ($request->has('service')) {
            $selectedService = $services->where('id', $request->get('service'))->first();
        }

        return view('landing-page.booking', compact('landingPage', 'business', 'services', 'selectedService'));
    }

    /**
     * Display booking page for a specific service.
     */
    public function bookService($businessSlug, $serviceId)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published || !$landingPage->booking_enabled) {
            abort(404, 'Booking not available for this business.');
        }

        $business = $landingPage->business;
        $service = $business->services()->where('id', $serviceId)->where('is_public', true)->first();

        if (!$service) {
            abort(404, 'Service not found or not available for booking.');
        }

        return view('landing-page.book-service', compact('landingPage', 'business', 'service'));
    }

    /**
     * Get landing page by business slug.
     */
    private function getLandingPageBySlug($businessSlug)
    {
        return Cache::remember("landing_page_{$businessSlug}", 3600, function () use ($businessSlug) {
            return BusinessLandingPage::with(['business', 'sections'])
                ->whereHas('business', function ($query) use ($businessSlug) {
                    $query->where('landing_page_slug', $businessSlug);
                })
                ->first();
        });
    }

    /**
     * Get service by slug within a business.
     */
    private function getServiceBySlug($business, $serviceSlug)
    {
        return $business->services()
            ->where('slug', $serviceSlug)
            ->where('is_public', true)
            ->with(['category', 'images', 'reviews'])
            ->first();
    }

    /**
     * Get business services for landing page display.
     */
    private function getBusinessServices($business)
    {
        return $business->services()
            ->where('is_public', true)
            ->where('is_active', true)
            ->with(['category', 'images'])
            ->orderBy('landing_display_order')
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get related services for a service.
     */
    private function getRelatedServices($service)
    {
        $relatedServiceIds = $service->related_services ?? [];

        if (empty($relatedServiceIds)) {
            // If no specific related services, get services from same category
            return $service->business->services()
                ->where('service_category_id', $service->service_category_id)
                ->where('id', '!=', $service->id)
                ->where('is_public', true)
                ->where('is_active', true)
                ->limit(3)
                ->get();
        }

        return $service->business->services()
            ->whereIn('id', $relatedServiceIds)
            ->where('is_public', true)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Get comprehensive page data for landing page.
     */
    private function getPageData($business)
    {
        return Cache::remember("page_data_{$business->id}", 1800, function () use ($business) {
            $serviceSettings = $business->landingServiceSettings;
            $displayCount = $serviceSettings->homepage_display_count ?? 6;

            return [
                'services' => $this->getBusinessServices($business)->take($displayCount),
                'testimonials' => $business->reviews()->where('is_featured', true)->limit(6)->get(),
                'gallery' => $business->galleryImages()->where('is_featured', true)->limit(12)->get(),
                'team' => $business->staff()->where('show_on_landing', true)->limit(8)->get(),
                'stats' => [
                    'total_services' => $business->services()->where('is_public', true)->count(),
                    'happy_customers' => $business->bookings()->distinct('customer_id')->count(),
                    'years_experience' => now()->diffInYears($business->created_at),
                    'total_bookings' => $business->bookings()->where('status', 'completed')->count(),
                ],
            ];
        });
    }

    /**
     * Track page view for analytics.
     */
    private function trackPageView($landingPage)
    {
        if ($landingPage->business->landingServiceSettings->track_service_analytics ?? true) {
            $landingPage->increment('page_views');
            $landingPage->update(['last_viewed_at' => now()]);
        }
    }

    /**
     * Track service view for analytics.
     */
    private function trackServiceView($service)
    {
        if ($service->business->landingServiceSettings->track_service_analytics ?? true) {
            $service->increment('landing_page_views');
            $service->update(['last_landing_view' => now()]);
        }
    }

    /**
     * Get availability for booking calendar.
     */
    public function getAvailability($businessSlug, Request $request)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published || !$landingPage->booking_enabled) {
            return response()->json(['error' => 'Booking not available'], 404);
        }

        $business = $landingPage->business;
        $date = $request->get('date');
        $serviceId = $request->get('service_id');

        if (!$date || !$serviceId) {
            return response()->json(['error' => 'Date and service ID required'], 400);
        }

        $service = $business->services()->where('id', $serviceId)->where('is_public', true)->first();

        if (!$service) {
            return response()->json(['error' => 'Service not found'], 404);
        }

        // Get available time slots for the date
        $timeSlots = $this->getAvailableTimeSlots($business, $service, $date);

        return response()->json([
            'date' => $date,
            'service_id' => $serviceId,
            'available_slots' => $timeSlots
        ]);
    }

    /**
     * Create a new booking.
     */
    public function createBooking($businessSlug, Request $request)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published || !$landingPage->booking_enabled) {
            return response()->json(['error' => 'Booking not available'], 404);
        }

        $business = $landingPage->business;

        $validated = $request->validate([
            'service_id' => 'required|exists:services,id',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required|string',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'notes' => 'nullable|string|max:1000',
        ]);

        $service = $business->services()->where('id', $validated['service_id'])->where('is_public', true)->first();

        if (!$service) {
            return response()->json(['error' => 'Service not found'], 404);
        }

        // Create the booking
        $booking = $business->bookings()->create([
            'service_id' => $service->id,
            'customer_name' => $validated['first_name'] . ' ' . $validated['last_name'],
            'customer_email' => $validated['email'],
            'customer_phone' => $validated['phone'],
            'appointment_date' => $validated['date'],
            'appointment_time' => $validated['time'],
            'notes' => $validated['notes'],
            'status' => 'pending',
            'booking_source' => 'landing_page',
            'total_amount' => $service->base_price,
        ]);

        return response()->json([
            'success' => true,
            'booking_id' => $booking->id,
            'reference' => 'BK' . str_pad($booking->id, 6, '0', STR_PAD_LEFT),
            'message' => 'Booking created successfully'
        ]);
    }

    /**
     * Get service details for booking.
     */
    public function getServiceDetails($businessSlug, $serviceId)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $business = $landingPage->business;
        $service = $business->services()->where('id', $serviceId)->where('is_public', true)->first();

        if (!$service) {
            return response()->json(['error' => 'Service not found'], 404);
        }

        return response()->json([
            'id' => $service->id,
            'name' => $service->name,
            'description' => $service->description,
            'duration' => $service->duration,
            'price' => $service->base_price,
            'currency' => $business->currency ?? 'USD',
            'preparation_instructions' => $service->preparation_instructions,
            'aftercare_instructions' => $service->aftercare_instructions,
        ]);
    }

    /**
     * Submit contact form.
     */
    public function submitContact($businessSlug, Request $request)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Store contact inquiry
        $business = $landingPage->business;
        $contact = $business->contactInquiries()->create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'subject' => $validated['subject'] ?? 'Contact Form Inquiry',
            'message' => $validated['message'],
            'source' => 'landing_page',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Thank you for your message. We will get back to you soon!'
        ]);
    }

    /**
     * Subscribe to newsletter.
     */
    public function subscribeNewsletter($businessSlug, Request $request)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $validated = $request->validate([
            'email' => 'required|email|max:255',
            'name' => 'nullable|string|max:255',
        ]);

        $business = $landingPage->business;

        // Check if already subscribed
        $existing = $business->newsletterSubscribers()->where('email', $validated['email'])->first();

        if ($existing) {
            return response()->json([
                'success' => true,
                'message' => 'You are already subscribed to our newsletter!'
            ]);
        }

        // Create subscription
        $business->newsletterSubscribers()->create([
            'email' => $validated['email'],
            'name' => $validated['name'],
            'subscribed_at' => now(),
            'source' => 'landing_page',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Thank you for subscribing to our newsletter!'
        ]);
    }

    /**
     * Display about page for a business.
     */
    public function about($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $aboutSection = $landingPage->sections()->where('section_type', 'about')->where('is_visible', true)->first();

        return view('landing-page.about', compact('landingPage', 'business', 'aboutSection'));
    }

    /**
     * Display contact page for a business.
     */
    public function contact($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $contactSection = $landingPage->sections()->where('section_type', 'contact')->where('is_visible', true)->first();

        return view('landing-page.contact', compact('landingPage', 'business', 'contactSection'));
    }

    /**
     * Display gallery page for a business.
     */
    public function gallery($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $galleryImages = $business->galleryImages()->where('is_active', true)->orderBy('sort_order')->get();

        return view('landing-page.gallery', compact('landingPage', 'business', 'galleryImages'));
    }

    /**
     * Display team page for a business.
     */
    public function team($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $teamMembers = $business->staff()->where('is_active', true)->where('show_on_landing', true)->orderBy('sort_order')->get();

        return view('landing-page.team', compact('landingPage', 'business', 'teamMembers'));
    }

    /**
     * Display reviews page for a business.
     */
    public function reviews($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $reviews = $business->reviews()->where('is_approved', true)->orderBy('created_at', 'desc')->paginate(12);

        return view('landing-page.reviews', compact('landingPage', 'business', 'reviews'));
    }

    /**
     * Generate sitemap for a business.
     */
    public function sitemap($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $business = $landingPage->business;
        $services = $this->getBusinessServices($business);

        return response()->view('landing-page.sitemap', compact('landingPage', 'business', 'services'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Generate robots.txt for a business.
     */
    public function robots($businessSlug)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            abort(404, 'Business landing page not found or not published.');
        }

        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Sitemap: " . route('landing-page.sitemap', $businessSlug) . "\n";

        return response($content)->header('Content-Type', 'text/plain');
    }

    /**
     * Search services for a business.
     */
    public function searchServices($businessSlug, Request $request)
    {
        $landingPage = $this->getLandingPageBySlug($businessSlug);

        if (!$landingPage || !$landingPage->is_published) {
            return response()->json(['error' => 'Business not found'], 404);
        }

        $business = $landingPage->business;
        $query = $request->get('q');

        if (!$query) {
            return response()->json(['services' => []]);
        }

        $services = $business->services()
            ->where('is_public', true)
            ->where('is_active', true)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->limit(10)
            ->get(['id', 'name', 'description', 'base_price', 'duration']);

        return response()->json(['services' => $services]);
    }

    /**
     * Get available time slots for a service on a specific date.
     */
    private function getAvailableTimeSlots($business, $service, $date)
    {
        // This is a simplified implementation
        // In a real application, you would check business hours, existing bookings, etc.

        $slots = [];
        $startHour = 9; // 9 AM
        $endHour = 17; // 5 PM
        $slotDuration = 30; // 30 minutes

        for ($hour = $startHour; $hour < $endHour; $hour++) {
            for ($minute = 0; $minute < 60; $minute += $slotDuration) {
                $time = sprintf('%02d:%02d', $hour, $minute);
                $slots[] = [
                    'time' => $time,
                    'available' => true, // In real implementation, check against bookings
                    'display_time' => date('g:i A', strtotime($time))
                ];
            }
        }

        return $slots;
    }
}
